import { Injectable, NotFoundException } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Repository } from 'typeorm';
import { TaskAnalyticService } from '../../../../../common/modules/analytics/task/services/task-analytic.service';
import { FormAnalyticService } from '../../../../../common/modules/analytics/form/services/form-analytic.service';
import { AlarmAnalyticService } from '../../../../../common/modules/analytics/alarm/services/alarm-analytic.service';
import { ActivityLogAnalyticService } from '../../../../../common/modules/analytics/activity/services/activity-log-analytic.service';
import { CheckpointActivityAnalyticService } from '../../../../../common/modules/analytics/checkpoint-activity/services/checkpoint-activity-analytic.service';
import { MobileLogHistoryDto } from '../dto/mobile-log-history.dto';
import { User } from '../../../../../common/modules/database/entities/user.entity';
import { Branch } from '../../../../../common/modules/database/entities/branch.entity';
import { Timezone } from '../../../../../common/modules/database/entities/timezone.entity';

@Injectable()
export class LogHistoryService {
  constructor(
    private readonly taskAnalyticService: TaskAnalyticService,
    private readonly formAnalyticService: FormAnalyticService,
    private readonly alarmAnalyticService: AlarmAnalyticService,
    private readonly activityLogAnalyticService: ActivityLogAnalyticService,
    private readonly checkpointActivityAnalyticService: CheckpointActivityAnalyticService,
    @InjectRepository(Branch)
    private readonly branchRepository: Repository<Branch>,
    @InjectRepository(Timezone)
    private readonly timezoneRepository: Repository<Timezone>,
  ) { }

  async findAll(query: MobileLogHistoryDto, user: User) {
    const { start_date, end_date } = query;

    // Get user's parent branch to get timezone
    const userBranch = await this.branchRepository.findOne({
      where: { id: user.parent_branch_id },
    });

    if (!userBranch) {
      throw new NotFoundException('User parent branch not found');
    }

    // Get timezone information
    const timezone = await this.timezoneRepository.findOne({
      where: { id: userBranch.timezone_id },
    });

    if (!timezone) {
      throw new NotFoundException('Timezone not found');
    }

    // Create array of promises for parallel execution
    const promises: Promise<any>[] = [];

    // Get task data
    promises.push(
      this.taskAnalyticService.getTasks(
        user.parent_branch_id,
        {
          startDate: start_date,
          endDate: end_date,
          startTime: '00:00:00',
          endTime: '23:59:59',
          userId: user.id,
          limit: 999999,
          page: 1,
          orderBy: 'original_submitted_time',
          orderDirection: 'DESC',
        },
        {
          timezone: timezone,
          user: user,
        },
      )
    );

    // Get form data
    promises.push(
      this.formAnalyticService.getFormLogs(
        user.parent_branch_id,
        {
          startDate: start_date || '',
          endDate: end_date || '',
          startTime: '00:00:00',
          endTime: '23:59:59',
          userId: user.id,
          limit: 999999,
          page: 1,
          orderBy: 'original_submitted_time',
          orderDirection: 'DESC',
        },
        {
          timezone: timezone,
          user: user,
        },
      )
    );

    // Get alarm data
    promises.push(
      this.alarmAnalyticService.getAlarmLogs(
        user.parent_branch_id,
        {
          startDate: start_date || '',
          endDate: end_date || '',
          startTime: '00:00:00',
          endTime: '23:59:59',
          userId: user.id,
          limit: 999999,
          page: 1,
          orderBy: 'original_submitted_time',
          orderDirection: 'DESC',
        },
        {
          timezone: timezone,
        },
      )
    );

    // Get activity data
    promises.push(
      this.activityLogAnalyticService.getActivityLogs(
        user.parent_branch_id,
        {
          startDate: start_date || '',
          endDate: end_date || '',
          startTime: '00:00:00',
          endTime: '23:59:59',
          userId: user.id,
          limit: 999999,
          page: 1,
          orderBy: 'original_submitted_time',
          orderDirection: 'DESC',
        },
        {
          timezone: timezone,
          user: user,
        },
      )
    );

    // Get checkpoint data
    promises.push(
      this.checkpointActivityAnalyticService.getCheckpointActivity(
        user.parent_branch_id,
        {
          startDate: start_date || '',
          endDate: end_date || '',
          startTime: '00:00:00',
          endTime: '23:59:59',
          userId: user.id,
          limit: 999999,
          page: 1,
          orderBy: 'original_submitted_time',
          orderDirection: 'DESC',
        },
        {
          timezone: timezone,
          user: user,
        },
      )
    );

    // Execute all promises in parallel
    const [taskData, formData, alarmData, activityData, checkpointData] = await Promise.all(promises);

    // Map task data
    const mappedTaskData = taskData.data.map((item: any) => ({
      type: 'task',
      event_name: 'Task Submitted',
      reference_name: item.task?.task_name || null,
      uuid: item.uuid,
      task_name: item.task?.task_name || null,
      user_id: Number(item.user_id) || null,
      user_name: item.user?.name || null,
      device_name: item.device?.device_name || null,
      timezone_id: item.timezone_id || null,
      timezone_name: item.timezone_name || null,
      latitude: item.latitude,
      longitude: item.longitude,
      original_submitted_time: item.original_submitted_time,
      fields: item.fields || [],
    }));

    // Map form data
    const mappedFormData = formData.data.map((item: any) => ({
      type: 'form',
      event_name: 'Form Submitted',
      reference_name: item.form?.form_name || null,
      uuid: item.uuid,
      form_name: item.form?.form_name || null,
      user_id: Number(item.user_id) || null,
      user_name: item.user?.name || null,
      device_name: item.device?.device_name || null,
      timezone_id: item.timezone_id || null,
      timezone_name: item.timezone_name || null,
      latitude: item.latitude,
      longitude: item.longitude,
      original_submitted_time: item.original_submitted_time,
      fields: item.fields || [],
    }));

    // Map alarm data
    const mappedAlarmData = alarmData.data.map((item: any) => ({
      type: 'alarm',
      event_name: 'Alarm Triggered',
      reference_name: 'Alarm Triggered',
      uuid: item.uuid,
      user_id: Number(item.user_id) || null,
      user_name: item.user?.name || null,
      device_name: item.device?.device_name || null,
      timezone_id: item.timezone_id || null,
      timezone_name: item.timezone_name || null,
      latitude: item.start_latitude,
      longitude: item.start_longitude,
      original_submitted_time: item.original_submitted_time,
    }));

    // Map activity data
    const mappedActivityData = activityData.data.map((item: any) => ({
      type: 'activity',
      event_name: 'Activity Submitted',
      reference_name: item.activity?.activity_name || null,
      uuid: item.uuid,
      activity_name: item.activity?.activity_name || null,
      user_id: Number(item.user_id) || null,
      user_name: item.user?.name || null,
      device_name: item.device?.device_name || null,
      timezone_id: item.timezone_id || null,
      timezone_name: item.timezone_name || null,
      latitude: item.latitude,
      longitude: item.longitude,
      photo_url: item.photo_url || null,
      comment: item.comment || null,
      original_submitted_time: item.original_submitted_time,
    }));

    // Map checkpoint data
    const mappedCheckpointData = checkpointData.data.map((item: any) => ({
      type: 'checkpoint',
      event_name: 'Checkpoint Scanned',
      reference_name: item.checkpoint?.checkpoint_name || null,
      uuid: item.uuid,
      checkpoint_name: item.checkpoint?.checkpoint_name || null,
      zone_name: item.zone?.zone_name || null,
      user_id: Number(item.user_id) || null,
      user_name: item.user?.name || null,
      device_name: item.device?.device_name || null,
      timezone_id: item.timezone_id || null,
      timezone_name: item.timezone_name || null,
      latitude: item.latitude,
      longitude: item.longitude,
      original_submitted_time: item.original_submitted_time,
    }));

    // Combine all data into one array and sort by original_submitted_time desc
    const combinedData = [
      ...mappedTaskData,
      ...mappedFormData,
      ...mappedAlarmData,
      ...mappedActivityData,
      ...mappedCheckpointData,
    ].sort((a, b) => {
      const dateA = new Date(a.original_submitted_time);
      const dateB = new Date(b.original_submitted_time);
      return dateB.getTime() - dateA.getTime(); // DESC order
    });

    return {
      data: combinedData,
    };
  }
} 