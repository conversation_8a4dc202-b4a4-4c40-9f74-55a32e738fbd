import { Injectable } from '@nestjs/common';
import { LogCheckpoint } from '../../../database/entities/log-checkpoint.entity';
import { LogAlert } from '../../../database/entities/log-alert.entity';
import { v4 as uuidv4 } from 'uuid';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Alert } from '../../../database/entities/alert.entity';

@Injectable()
export class CheckpointActivityLogAlertService {
  constructor(
    @InjectRepository(LogAlert)
    private readonly logAlertRepository: Repository<LogAlert>,
  ) {}

  public async createLogAlert(logCheckpoint: LogCheckpoint, alert: Alert) {
    // Check if logCheckpoint already has a logAlert
    const existingLogAlert = await this.logAlertRepository.findOne({
      where: { log_uuid: logCheckpoint.uuid },
    });

    if (existingLogAlert) {
      return existingLogAlert;
    }

    const logAlert = new LogAlert();

    logAlert.uuid = uuidv4();
    logAlert.alert_event_id = 5;
    logAlert.alert_event_name = 'Checkpoint Scanned';
    logAlert.log_id = logCheckpoint.id;
    logAlert.log_uuid = logCheckpoint.uuid;
    logAlert.alert_name = alert.alert_name;
    logAlert.reference_name = logCheckpoint.checkpoint_name;
    logAlert.parent_branch_id = logCheckpoint.parent_branch_id;
    logAlert.branch_id = logCheckpoint.branch_id;
    logAlert.branch_name = logCheckpoint.branch_name;
    logAlert.device_id = logCheckpoint.device_id;
    logAlert.device_name = logCheckpoint.device_name;
    logAlert.latitude = logCheckpoint.latitude;
    logAlert.longitude = logCheckpoint.longitude;
    logAlert.timezone_id = logCheckpoint.timezone_id;
    logAlert.timezone_name = logCheckpoint.timezone_name;
    logAlert.user_id = logCheckpoint.user_id;
    logAlert.user_name = logCheckpoint.user_name;
    logAlert.role_id = logCheckpoint.role_id;
    logAlert.role_name = logCheckpoint.role_name;
    logAlert.payload_data = {
      type: 'checkpoint',
      logCheckpoint: logCheckpoint,
    };
    logAlert.event_time = logCheckpoint.event_time;
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logCheckpoint.original_submitted_time;

    await this.logAlertRepository.save(logAlert);

    return logAlert;
  }
}
