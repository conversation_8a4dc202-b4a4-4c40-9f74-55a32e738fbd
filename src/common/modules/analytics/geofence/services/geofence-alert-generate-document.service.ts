import * as PDFDocument from 'pdfkit';
import { Injectable } from '@nestjs/common';
import { LogGeolocation } from '../../../database/entities/log-geolocation.entity';

@Injectable()
export class GeofenceAlertGenerateDocumentService {
  constructor() {}

  async generatePDF(logGeolocation: LogGeolocation, title: string): Promise<{ buffer: Buffer; filename: string }> {

  }

  private createPdfDoc = () => {
    return new PDFDocument({
      size: 'A4',
      autoFirstPage: false,
      margins: {
        top: 10,
        bottom: 10,
        left: 10,
        right: 10,
      },
      info: {
        Title: 'Geofence Alert Report',
        Author: 'UniGuard System',
        Creator: 'UniGuard',
      },
    });
  };
}
