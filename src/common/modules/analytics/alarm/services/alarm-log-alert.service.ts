import { Injectable } from '@nestjs/common';
import { LogAlarm } from '../../../database/entities/log-alarm.entity';
import { User } from '../../../database/entities/user.entity';
import { LogAlert } from '../../../database/entities/log-alert.entity';
import { v4 as uuidv4 } from 'uuid';
import { Repository } from 'typeorm';
import { InjectRepository } from '@nestjs/typeorm';
import { Alert } from '../../../database/entities/alert.entity';

@Injectable()
export class AlarmLogAlertService {
  constructor(
    @InjectRepository(LogAlert)
    private readonly logAlertRepository: Repository<LogAlert>,
  ) {}

  public async createLogAlert(
    logAlarm: LogAlarm,
    alert: Alert,
  ) {
    // Check if logAlarm already has a logAlert
    const existingLogAlert = await this.logAlertRepository.findOne({
      where: { log_uuid: logAlarm.uuid },
    });

    if (existingLogAlert) {
      return existingLogAlert;
    }

    const logAlert = new LogAlert();

    logAlert.uuid = uuidv4();
    logAlert.alert_event_id = 4;
    logAlert.alert_event_name = 'Alarm Started';
    logAlert.log_id = logAlarm.id;
    logAlert.log_uuid = logAlarm.uuid;
    logAlert.reference_name = 'Alarm Started';
    logAlert.parent_branch_id = logAlarm.parent_branch_id;
    logAlert.branch_id = logAlarm.branch_id;
    logAlert.branch_name = logAlarm.branch_name;
    logAlert.user_id = logAlarm.user_id;
    logAlert.user_name = logAlarm.user_name;
    logAlert.role_id = logAlarm.role_id;
    logAlert.role_name = logAlarm.role_name;
    logAlert.device_id = logAlarm.device_id;
    logAlert.device_name = logAlarm.device_name;
    logAlert.latitude = logAlarm.start_latitude;
    logAlert.longitude = logAlarm.start_longitude;
    logAlert.timezone_id = logAlarm.timezone_id;
    logAlert.timezone_name = logAlarm.timezone_name;
    logAlert.payload_data = {
      type: 'alarm',
      logAlarm: logAlarm,
    };
    logAlert.event_time = logAlarm.event_time;
    logAlert.deleted_on_dashboard = false;
    logAlert.original_submitted_time = logAlarm.original_submitted_time;

    await this.logAlertRepository.save(logAlert);

    return logAlert;
  }
}
