import { Injectable } from '@nestjs/common';
import { InjectRepository } from '@nestjs/typeorm';
import { Brackets, Repository, SelectQueryBuilder } from 'typeorm';
import { Alert } from '../../database/entities/alert.entity';
import { SendgridService } from '../../mailer/sendgrid/services/sendgrid.service';
import { SendgridSendMessageInterface } from '../../mailer/sendgrid/interfaces/sendgird.interface';
import { IAlertParameters } from '../interfaces/alert-common.interface';
import { ActivityLogGenerateDocumentService } from '../../analytics/activity/services/activity-log-generate-document.service';
import { Timezone } from '../../database/entities/timezone.entity';
import { TaskLogGenerateDocumentService } from '../../analytics/task/services/task-log-generate-document.service';
import { FormsLogGenerateDocumentService } from '../../analytics/form/services/forms-log-generate-document.service';
import { AlarmLogGenerateDocumentService } from '../../analytics/alarm/services/alarm-log-generate-document.service';
import { CheckpointActivityLogGenerateDocumentService } from '../../analytics/checkpoint-activity/services/checkpoint-activity-log-generate-document.service';
import { SignInOutLogGenerateDocumentService } from '../../analytics/sign-in-out/services/sign-in-out-log-generate-document.service';
import * as dayjs from 'dayjs';
import * as utc from 'dayjs/plugin/utc';
import * as timezone from 'dayjs/plugin/timezone';
import { ActivityLogAlertService } from '../../analytics/activity/services/activity-log-alert.service';
import { TaskLogAlertService } from '../../analytics/task/services/task-log-alert.service';
import { FormLogAlertService } from '../../analytics/form/services/form-log-alert.service';
import { AlarmLogAlertService } from '../../analytics/alarm/services/alarm-log-alert.service';
import { CheckpointActivityLogAlertService } from '../../analytics/checkpoint-activity/services/checkpoint-activity-log-alert.service';
import { SignInOutLogAlertService } from '../../analytics/sign-in-out/services/sign-in-out-log-alert.service';
import { FormBranch } from '../../database/entities/form-branch.entity';
import { UserBranch } from '../../database/entities/user-branch.entity';

dayjs.extend(utc);
dayjs.extend(timezone);

@Injectable()
export class AlertCommonService {
  private alertQueryBuilder: SelectQueryBuilder<Alert>;

  constructor(
    @InjectRepository(Alert)
    private alertRepository: Repository<Alert>,
    private activityLogGenerateDocumentService: ActivityLogGenerateDocumentService,
    @InjectRepository(FormBranch)
    private formBranchRepository: Repository<FormBranch>,
    @InjectRepository(UserBranch)
    private userBranchRepository: Repository<UserBranch>,
    private taskLogGenerateDocumentService: TaskLogGenerateDocumentService,
    private formLogGenerateDocumentService: FormsLogGenerateDocumentService,
    private alarmLogGenerateDocumentService: AlarmLogGenerateDocumentService,
    private checkpointActivityLogGenerateDocumentService: CheckpointActivityLogGenerateDocumentService,
    private signInOutLogGenerateDocumentService: SignInOutLogGenerateDocumentService,
    private activityLogAlertService: ActivityLogAlertService,
    private taskLogAlertService: TaskLogAlertService,
    private formLogAlertService: FormLogAlertService,
    private alarmLogAlertService: AlarmLogAlertService,
    private checkpointActivityLogAlertService: CheckpointActivityLogAlertService,
    private signInOutLogAlertService: SignInOutLogAlertService,
    private sendgridService: SendgridService,
  ) {
    this.alertQueryBuilder = this.alertRepository
      .createQueryBuilder('alert')
      .leftJoinAndSelect('alert.recipients', 'recipients')
      .innerJoin('alert_branches', 'ab', 'ab.alert_id = alert.id')
      .innerJoinAndSelect('alert.parent_branch', 'parent_branch')
      .innerJoinAndSelect('parent_branch.timezone', 'timezone')
      .leftJoin('alert.conditions', 'conditions')
      .andWhere('alert.active = :active', { active: true });
  }

  async processAlert(logData: IAlertParameters) {
    // Get matching 'or' alerts
    const matchingOrAlerts = await this.getAlerts(logData, 'OR');

    // Get matching 'and' alerts
    const matchingAndAlerts = await this.getAlerts(logData, 'AND');

    // Combine alerts
    const alerts = [...matchingOrAlerts, ...matchingAndAlerts];

    // Process alerts (send emails, etc.)
    for (const alert of alerts) {
      new Promise(async (resolve, reject) => {
        // Send to Email
        if (parseInt(alert.alert_action_id as any) === 1) {
          const createPdf = await this.generatePdf(
            logData,
            alert.parent_branch.timezone,
          );

          // Send email
          const payloadSendEmails: SendgridSendMessageInterface = {
            to: alert.recipients.map(recipient => recipient.recipient_contact),
            subject: alert.subject,
            html: alert.message,
            text: alert.message,
          };

          if (createPdf) {
            payloadSendEmails.attachments = [
              {
                content: createPdf.buffer.toString('base64'),
                filename: createPdf.filename,
                type: 'application/pdf',
                disposition: 'attachment',
              },
            ];
            await this.sendgridService.sendEmail(payloadSendEmails);
          }
        }
        // Send To Dashboard
        else if (parseInt(alert.alert_action_id as any) === 3) {
          await this.insertLogAlert(logData, alert);
        }

        resolve(null);
      })
        .then()
        .catch(err => {
          console.log('Error sending email: ', err);
        });
    }

    return alerts;
  }

  private async getAlerts(logData: IAlertParameters, condition: 'OR' | 'AND') {
    // Extract common properties
    const {
      userId,
      roleId,
      checkpointId,
      geofenceId,
      zoneId,
      deviceId,
      parentBranchId,
      alertEventId,
      submittedDateTime,
    } = logData;

    let timeZoneName: string | undefined;

    if (logData.logActivity) {
      timeZoneName = logData.logActivity.timezone_name;
    } else if (logData.logTask) {
      timeZoneName = logData.logTask.timezone_name;
    } else if (logData.logForm) {
      timeZoneName = logData.logForm.timezone_name;
    } else if (logData.logAlarm) {
      timeZoneName = logData.logAlarm.timezone_name;
    } else if (logData.logCheckpoint) {
      timeZoneName = logData.logCheckpoint.timezone_name;
    } else if (logData.logSignInOut) {
      timeZoneName = logData.logSignInOut.timezone_name;
    }

    const branchIds = await this.generateBranches(logData);

    const dateTime = dayjs(submittedDateTime).tz(timeZoneName);
    const dayOfMonth = dateTime.date();
    const dayOfWeek = dateTime.day();
    const hours = dateTime.hour();
    const minutes = dateTime.minute();

    // console.log({
    //   dateTime: dateTime.format(),
    //   dayOfMonth,
    //   dayOfWeek,
    //   hours,
    //   minutes,
    // })

    const alertQueryBuilder = this.alertQueryBuilder
      .clone()
      .andWhere('alert.alert_event_id = :alertEventId', {
        alertEventId: alertEventId,
      })
      .andWhere('alert.parent_branch_id = :parentBranchId', {
        parentBranchId: parentBranchId,
      })
      .andWhere('ab.branch_id IN (:...branchIds)', { branchIds });

    if (condition === 'OR') {
      // Apply OR conditions
      const orConditions = alertQueryBuilder
        .andWhere(
          'alert.alert_logical_condition_type = :alertLogicalConditionType',
          {
            alertLogicalConditionType: 'OR',
          },
        )
        .andWhere(
          new Brackets(qb => {
            qb.where(
              'NOT EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id)',
            ).orWhere(
              new Brackets(qb => {
                // User condition
                qb.where(this.userBracketCondition(userId, condition));

                // Role condition
                qb.orWhere(this.roleBracketCondition(roleId, condition));

                // Day of month condition
                qb.orWhere(
                  this.dayOfMonthBracketCondition(dayOfMonth, condition),
                );

                // Day of week condition
                qb.orWhere(
                  this.dayOfWeekBracketCondition(dayOfWeek, condition),
                );

                // Hours condition
                qb.orWhere(this.hoursBracketCondition(hours, condition));

                // Minutes condition
                qb.orWhere(this.minutesBracketCondition(minutes, condition));

                // Checkpoint condition
                qb.orWhere(
                  this.checkpointBracketCondition(
                    checkpointId || null,
                    condition,
                  ),
                );

                // Geofence condition
                // qb.orWhere(
                //   this.geofenceBracketCondition(geofenceId, condition),
                // );

                // Zone condition
                // qb.orWhere(this.zoneBracketCondition(zoneId, condition));

                // Device condition
                qb.orWhere(this.deviceBracketCondition(deviceId, condition));
              }),
            );
          }),
        );

      return orConditions.getMany();
    } else {
      // Apply AND conditions
      const andConditions = alertQueryBuilder
        .andWhere(
          'alert.alert_logical_condition_type = :alertLogicalConditionType',
          {
            alertLogicalConditionType: 'AND',
          },
        )
        .andWhere(
          new Brackets(qb => {
            qb.where(
              'NOT EXISTS (SELECT 1 FROM alert_conditions ac WHERE ac.alert_id = alert.id)',
            ).orWhere(
              new Brackets(qb => {
                // User condition
                qb.where(this.userBracketCondition(userId, condition));

                // Role condition
                qb.andWhere(this.roleBracketCondition(roleId, condition));

                // Day of month condition
                qb.andWhere(
                  this.dayOfMonthBracketCondition(dayOfMonth, condition),
                );

                // Day of week condition
                qb.andWhere(
                  this.dayOfWeekBracketCondition(dayOfWeek, condition),
                );

                // Hours condition
                qb.andWhere(this.hoursBracketCondition(hours, condition));

                // Minutes condition
                qb.andWhere(this.minutesBracketCondition(minutes, condition));

                // Checkpoint condition
                qb.andWhere(
                  this.checkpointBracketCondition(
                    checkpointId || null,
                    condition,
                  ),
                );

                // Geofence condition
                // qb.andWhere(
                //   this.geofenceBracketCondition(geofenceId, condition),
                // );

                // Zone condition
                // qb.andWhere(this.zoneBracketCondition(zoneId, condition));

                // Device condition
                qb.andWhere(this.deviceBracketCondition(deviceId, condition));
              }),
            );
          }),
        );
      return andConditions.getMany();
    }
  }

  private async generateBranches(logData: IAlertParameters) {
    let userBranches: UserBranch[] = [];
    if ([6, 7, 4].includes(logData.alertEventId)) {
      userBranches = await this.userBranchRepository.find({
        where: { user_id: logData.userId },
      });
    }

    const branchIds: number[] = [];
    if (logData.alertEventId === 1 && logData.logActivity) {
      branchIds.push(logData.logActivity.branch_id);
    } else if (logData.alertEventId === 2 && logData.logTask) {
      branchIds.push(logData.logTask.branch_id);
    } else if (logData.alertEventId === 3 && logData.logForm) {
      const formBranches = await this.formBranchRepository.find({
        where: { form_id: logData.logForm.form_id },
      });
      formBranches.forEach(formBranch => {
        branchIds.push(formBranch.branch_id);
      });
    } else if (logData.alertEventId === 4 && logData.logAlarm) {
      branchIds.push(...userBranches.map(userBranch => userBranch.branch_id));
    } else if (logData.alertEventId === 5 && logData.logCheckpoint) {
      branchIds.push(logData.logCheckpoint.branch_id);
    } else if ([6, 7].includes(logData.alertEventId) && logData.logSignInOut) {
      branchIds.push(...userBranches.map(userBranch => userBranch.branch_id));
    }

    return branchIds;
  }

  // User condition (alert_condition_type_id = 1)
  private userBracketCondition(userId: number, condition: 'OR' | 'AND') {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
                SELECT 1 FROM alert_conditions ac_User
                WHERE ac_User.alert_id = alert.id
                AND ac_User.alert_condition_type_id = 1
                AND (
                  (ac_User.alert_operator_condition_type = '=' AND ac_User.alert_condition_value_id = :userId)
                  OR
                  (ac_User.alert_operator_condition_type = '!=' AND ac_User.alert_condition_value_id != :userId)
                )
              )
          `,
        {
          userId,
        },
      );
      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_User
            WHERE ac_User.alert_id = alert.id
            AND ac_User.alert_condition_type_id = 1
          )
          `,
        );
      }
    });
  }

  // Role condition (alert_condition_type_id = 2)
  private roleBracketCondition(roleId: number, condition: 'OR' | 'AND') {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
            SELECT 1 FROM alert_conditions ac_Role
            WHERE ac_Role.alert_id = alert.id
            AND ac_Role.alert_condition_type_id = 2
            AND (
              (ac_Role.alert_operator_condition_type = '=' AND ac_Role.alert_condition_value_id = :roleId)
              OR
              (ac_Role.alert_operator_condition_type = '!=' AND ac_Role.alert_condition_value_id != :roleId)
            )
          )
          `,
        {
          roleId,
        },
      );

      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_Role
            WHERE ac_Role.alert_id = alert.id
            AND ac_Role.alert_condition_type_id = 2
          )
          `,
        );
      }
    });
  }

  // Day of month condition (alert_condition_type_id = 3)
  private dayOfMonthBracketCondition(
    dayOfMonth: number,
    condition: 'OR' | 'AND',
  ) {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
            SELECT 1 FROM alert_conditions ac_DayOfMonth
            WHERE ac_DayOfMonth.alert_id = alert.id
            AND ac_DayOfMonth.alert_condition_type_id = 3
            AND (
              (ac_DayOfMonth.alert_operator_condition_type = '=' AND ac_DayOfMonth.alert_condition_value_id = :dayOfMonth)
              OR
              (ac_DayOfMonth.alert_operator_condition_type = '!=' AND ac_DayOfMonth.alert_condition_value_id != :dayOfMonth)
            )
          )
          `,
        {
          dayOfMonth,
        },
      );
      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_DayOfMonth
            WHERE ac_DayOfMonth.alert_id = alert.id
            AND ac_DayOfMonth.alert_condition_type_id = 3
          )
          `,
        );
      }
    });
  }

  // Day of week condition (alert_condition_type_id = 4)
  private dayOfWeekBracketCondition(
    dayOfWeek: number,
    condition: 'OR' | 'AND',
  ) {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
            SELECT 1 FROM alert_conditions ac_DayOfWeek
            WHERE ac_DayOfWeek.alert_id = alert.id
            AND ac_DayOfWeek.alert_condition_type_id = 4
            AND (
              (ac_DayOfWeek.alert_operator_condition_type = '=' AND ac_DayOfWeek.alert_condition_value_id = :dayOfWeek)
              OR
              (ac_DayOfWeek.alert_operator_condition_type = '!=' AND ac_DayOfWeek.alert_condition_value_id != :dayOfWeek)
            )
          )
          `,
        {
          dayOfWeek,
        },
      );
      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_DayOfWeek
            WHERE ac_DayOfWeek.alert_id = alert.id
            AND ac_DayOfWeek.alert_condition_type_id = 4
          )
          `,
        );
      }
    });
  }

  // Hours condition (alert_condition_type_id = 5)
  private hoursBracketCondition(hours: number, condition: 'OR' | 'AND') {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
            SELECT 1 FROM alert_conditions ac_Hours
            WHERE ac_Hours.alert_id = alert.id
            AND ac_Hours.alert_condition_type_id = 5
            AND (
              (ac_Hours.alert_operator_condition_type = '=' AND ac_Hours.alert_condition_value_id = :hours)
              OR
              (ac_Hours.alert_operator_condition_type = '!=' AND ac_Hours.alert_condition_value_id != :hours)
            )
          )
          `,
        {
          hours,
        },
      );
      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_Hours
            WHERE ac_Hours.alert_id = alert.id
            AND ac_Hours.alert_condition_type_id = 5
          )
          `,
        );
      }
    });
  }

  // Minutes condition (alert_condition_type_id = 6)
  private minutesBracketCondition(minutes: number, condition: 'OR' | 'AND') {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
            SELECT 1 FROM alert_conditions ac_Minutes
            WHERE ac_Minutes.alert_id = alert.id
            AND ac_Minutes.alert_condition_type_id = 6
            AND (
              (ac_Minutes.alert_operator_condition_type = '=' AND ac_Minutes.alert_condition_value_id = :minutes)
              OR 
              (ac_Minutes.alert_operator_condition_type = '!=' AND ac_Minutes.alert_condition_value_id != :minutes)
            )
          )
          `,
        {
          minutes,
        },
      );
      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_Minutes
            WHERE ac_Minutes.alert_id = alert.id
            AND ac_Minutes.alert_condition_type_id = 6
          )
          `,
        );
      }
    });
  }

  // Checkpoint condition (alert_condition_type_id = 7)
  private checkpointBracketCondition(
    checkpointId: number | null,
    condition: 'OR' | 'AND',
  ) {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
            SELECT 1 FROM alert_conditions ac_Checkpoint
            WHERE ac_Checkpoint.alert_id = alert.id
            AND ac_Checkpoint.alert_condition_type_id = 7
            AND (
              (ac_Checkpoint.alert_operator_condition_type = '=' AND ac_Checkpoint.alert_condition_value_id = :checkpointId)
              OR
              (ac_Checkpoint.alert_operator_condition_type = '!=' AND ac_Checkpoint.alert_condition_value_id != :checkpointId)
            )
          )
          `,
        {
          checkpointId,
        },
      );
      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_Checkpoint
            WHERE ac_Checkpoint.alert_id = alert.id
            AND ac_Checkpoint.alert_condition_type_id = 7
          )
          `,
        );
      }
    });
  }

  // Geofence condition (alert_condition_type_id = 8)
  private geofenceBracketCondition(
    geofenceId: number,
    condition: 'OR' | 'AND',
  ) {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
            SELECT 1 FROM alert_conditions ac_Geofence
            WHERE ac_Geofence.alert_id = alert.id
            AND ac_Geofence.alert_condition_type_id = 8
            AND (
              (ac_Geofence.alert_operator_condition_type = '=' AND ac_Geofence.alert_condition_value_id = :geofenceId)
              OR
              (ac_Geofence.alert_operator_condition_type = '!=' AND ac_Geofence.alert_condition_value_id != :geofenceId)
            )
          )
          `,
        {
          geofenceId,
        },
      );
      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_Geofence
            WHERE ac_Geofence.alert_id = alert.id
            AND ac_Geofence.alert_condition_type_id = 8
          )
          `,
        );
      }
    });
  }

  // Zone condition (alert_condition_type_id = 9)
  private zoneBracketCondition(zoneId: number, condition: 'OR' | 'AND') {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
            SELECT 1 FROM alert_conditions ac_Zone
            WHERE ac_Zone.alert_id = alert.id
            AND ac_Zone.alert_condition_type_id = 9
            AND (
              (ac_Zone.alert_operator_condition_type = '=' AND ac_Zone.alert_condition_value_id = :zoneId)
              OR
              (ac_Zone.alert_operator_condition_type = '!=' AND ac_Zone.alert_condition_value_id != :zoneId)
            )
          )
          `,
        {
          zoneId,
        },
      );
      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_Zone
            WHERE ac_Zone.alert_id = alert.id
            AND ac_Zone.alert_condition_type_id = 9
          )
          `,
        );
      }
    });
  }

  // Device condition (alert_condition_type_id = 10)
  private deviceBracketCondition(
    deviceId: number | null,
    condition: 'OR' | 'AND',
  ) {
    return new Brackets(qb => {
      qb.where(
        `
          EXISTS (
            SELECT 1 FROM alert_conditions ac_Device
            WHERE ac_Device.alert_id = alert.id
            AND ac_Device.alert_condition_type_id = 10
            AND (
              (ac_Device.alert_operator_condition_type = '=' AND ac_Device.alert_condition_value_id = :deviceId)
              OR
              (ac_Device.alert_operator_condition_type = '!=' AND ac_Device.alert_condition_value_id != :deviceId)
            )
          )
          `,
        {
          deviceId,
        },
      );
      if (condition === 'AND') {
        qb.orWhere(
          `
          NOT EXISTS (
            SELECT 1 FROM alert_conditions ac_Device
            WHERE ac_Device.alert_id = alert.id
            AND ac_Device.alert_condition_type_id = 10
          )
          `,
        );
      }
    });
  }

  // Generate PDF
  private generatePdf(
    params: Pick<
      IAlertParameters,
      | 'alertEventId'
      | 'logActivity'
      | 'logTask'
      | 'logForm'
      | 'logAlarm'
      | 'logCheckpoint'
      | 'logSignInOut'
    >,
    timezone: Timezone,
  ) {
    // Activity
    if (params.alertEventId === 1 && params.logActivity) {
      return this.activityLogGenerateDocumentService.generatePDFById(
        params.logActivity,
        timezone,
      );
    } else if (params.alertEventId === 2 && params.logTask) {
      // Task
      return this.taskLogGenerateDocumentService.generatePDFById(
        params.logTask,
        timezone,
      );
    } else if (params.alertEventId === 3 && params.logForm) {
      // Form
      return this.formLogGenerateDocumentService.generatePDFById(
        params.logForm,
        timezone,
      );
    } else if (params.alertEventId === 4 && params.logAlarm) {
      // Alarm
      return this.alarmLogGenerateDocumentService.generatePDFById(
        params.logAlarm,
        timezone,
      );
    } else if (params.alertEventId === 5 && params.logCheckpoint) {
      // Checkpoint
      return this.checkpointActivityLogGenerateDocumentService.generatePDFById(
        params.logCheckpoint,
        timezone,
      );
    } else if ([6, 7].includes(params.alertEventId) && params.logSignInOut) {
      // Sign In/Out
      return this.signInOutLogGenerateDocumentService.generatePDFById(
        params.logSignInOut,
        timezone,
      );
    }
  }

  private insertLogAlert(
    params: Pick<
      IAlertParameters,
      | 'alertEventId'
      | 'logActivity'
      | 'logTask'
      | 'logForm'
      | 'logAlarm'
      | 'logCheckpoint'
      | 'logSignInOut'
    >,
    alert: Alert,
  ) {
    if (params.alertEventId === 1 && params.logActivity) {
      return this.activityLogAlertService.createLogAlert(params.logActivity, alert);
    } else if (params.alertEventId === 2 && params.logTask) {
      return this.taskLogAlertService.createLogAlert(params.logTask, alert);
    } else if (params.alertEventId === 3 && params.logForm) {
      return this.formLogAlertService.createLogAlert(params.logForm, alert);
    } else if (params.alertEventId === 4 && params.logAlarm) {
      return this.alarmLogAlertService.createLogAlert(params.logAlarm, alert);
    } else if (params.alertEventId === 5 && params.logCheckpoint) {
      return this.checkpointActivityLogAlertService.createLogAlert(
        params.logCheckpoint,
        alert,
      );
    } else if ([6, 7].includes(params.alertEventId) && params.logSignInOut) {
      return this.signInOutLogAlertService.createLogAlert(params.logSignInOut, alert);
    }
  }
}
